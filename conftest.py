"""集中管理固件，pytest 会自动调用"""
import pytest
from loguru import logger
from common.config import get_config_by_env, get_config_by_custom_url, is_custom_url


def pytest_addoption(parser):
    """注册 --env 命令行参数，用于选择测试环境或指定自定义 URL。"""
    parser.addoption(
        "--env",
        action="store",
        default="alphanet",  # 默认运行 alphanet 环境
        help="选择网络环境 (例如: alphanet, stage) 或输入自定义 URL/IP"
    )


@pytest.fixture(scope="session")
def env(pytestconfig):
    """根据 --env 参数加载相应的配置。

    如果 --env 的值被识别为 URL 或 IP 地址，则会加载自定义配置；
    否则，会尝试作为预定义的环境名称加载配置。
    """
    env_input = pytestconfig.getoption("--env")

    if is_custom_url(env_input):
        logger.info(f'检测到自定义 URL/IP 输入: {env_input}')
        return get_config_by_custom_url(env_input)
    else:
        logger.info(f'尝试加载预定义环境: {env_input}')
        return get_config_by_env(env_input)

# 标记测试任务的开始和结束
@pytest.fixture(scope='session', autouse=True)
def task_mark():
    logger.debug("======================测试任务开始======================")
    yield
    logger.debug("======================测试任务结束======================")

# 标记测试用例的开始和结束
@pytest.fixture(autouse=True)
def case_mark():
    logger.debug("======================用例开始======================")
    yield
    logger.debug("======================用例结束======================")

# 测试会话结束后执行的操作
# def pytest_sessionfinish():
    # 配置日志记录器，日志文件最大50MB，保留一周
    # logger.add(BASE_DIR + '/log/{time}.log', rotation='50 MB', retention='1 week', encoding='utf-8')


