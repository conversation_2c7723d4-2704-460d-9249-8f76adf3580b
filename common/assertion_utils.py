from loguru import logger


def assert_status_and_nodeid(response):
    try:
        assert response.status_code == 200, f"响应状态码错误，预期 200，实际 {response.status_code}"
        assert 'X-Node-Id' in response.headers, "响应头缺少 'X-Node-Id'"
        assert response.headers['X-Node-Id'], "'X-Node-Id' 响应头值为空"
        assert '0x' in response.headers['X-Node-Id'], f"'X-Node-Id' ({response.headers['X-Node-Id']}) 不包含 '0x'"
        logger.info(
            f"断言成功: 状态码={response.status_code}, X-Node-Id={response.headers.get('X-Node-Id')}"
        )
    except AssertionError as e:
        logger.error(
            f"eq断言失败，预期结果：200，实际结果：{response.status_code if response else 'N/A'}"
        )
        logger.error(f"用例失败！断言信息: {e}")
        if response is not None:
            logger.debug(
                f"失败响应详情: Status={response.status_code}, Headers={response.headers}, Body={response.text[:500]}..."
            )
        raise e


def assert_contains(content, expected='0x'):
    try:
        if isinstance(expected, list):
            if all(isinstance(item, str) for item in expected):
                content_str = str(content)
                assert any(item in content_str for item in expected), (
                    f"字符串包含断言失败 - 未找到期望的内容: {expected}\n"
                    f"实际内容: {content_str[:200]}{'...' if len(content_str) > 200 else ''}"
                )
            else:
                assert isinstance(content, list), (
                    f"数组类型断言失败 - 实际内容不是列表\n"
                    f"期望类型: list, 实际类型: {type(content).__name__}\n"
                    f"实际内容: {content}"
                )
                assert len(content) == len(expected), (
                    f"数组长度断言失败\n"
                    f"期望长度: {len(expected)}, 实际长度: {len(content)}\n"
                    f"期望内容: {expected}\n"
                    f"实际内容: {content}"
                )
                for exp_item, actual_item in zip(expected, content):
                    if isinstance(exp_item, dict) and 'result' in exp_item:
                        if isinstance(exp_item['result'], str) and exp_item['result'].endswith('*'):
                            prefix = exp_item['result'][:-1]
                            if (
                                isinstance(actual_item, dict)
                                and 'result' in actual_item
                                and isinstance(actual_item['result'], str)
                            ):
                                assert actual_item['result'].startswith(prefix), (
                                    f"前缀匹配断言失败\n"
                                    f"期望前缀: {prefix}\n"
                                    f"实际值: {actual_item['result']}\n"
                                    f"完整期望项: {exp_item}\n"
                                    f"完整实际项: {actual_item}"
                                )
                            else:
                                assert (
                                    isinstance(actual_item, dict)
                                    and 'result' in actual_item
                                    and isinstance(actual_item['result'], dict)
                                ), (
                                    f"字典结构断言失败\n"
                                    f"期望: 包含 'result' 字段的字典类型\n"
                                    f"实际值: {actual_item}\n"
                                    f"实际类型: {type(actual_item).__name__}"
                                )
                        else:
                            assert (
                                isinstance(actual_item, dict)
                                and 'result' in actual_item
                                and exp_item['result'] == actual_item['result']
                            ), (
                                f"结果值断言失败\n"
                                f"期望结果: {exp_item['result']}\n"
                                f"实际结果: {actual_item.get('result', 'MISSING_RESULT_FIELD')}\n"
                                f"完整期望项: {exp_item}\n"
                                f"完整实际项: {actual_item}"
                            )
                    else:
                        assert exp_item == actual_item, (
                            f"数组元素断言失败\n"
                            f"期望: {exp_item} (类型: {type(exp_item).__name__})\n"
                            f"实际: {actual_item} (类型: {type(actual_item).__name__})"
                        )
        elif isinstance(expected, str):
            content_str = str(content)
            assert expected in content_str, (
                f"字符串包含断言失败\n"
                f"期望包含: '{expected}'\n"
                f"实际内容: {content_str[:300]}{'...' if len(content_str) > 300 else ''}\n"
                f"内容长度: {len(content_str)}"
            )
        else:
            raise TypeError(
                f"参数类型错误 - expected 必须是字符串、字符串列表或包含字典的列表\n"
                f"实际类型: {type(expected).__name__}\n"
                f"实际值: {expected}"
            )
    except AssertionError as e:
        logger.error(f"assert_contains 断言失败: {e}")
        logger.error("用例失败！")
        raise e
    except Exception as e:
        logger.error(f"assert_contains 执行异常: {type(e).__name__}: {e}")
        logger.error(f"断言参数 - expected: {expected}, content: {content}")
        raise e


def assert_id_and_version(content):
    try:
        if not isinstance(content, dict):
            raise AssertionError(
                f"响应内容类型错误 - 期望字典类型，实际类型: {type(content).__name__}"
            )
        missing_fields = []
        if 'id' not in content:
            missing_fields.append('id')
        if 'jsonrpc' not in content:
            missing_fields.append('jsonrpc')
        if missing_fields:
            raise AssertionError(
                f"响应缺少必需字段: {missing_fields}\n"
                f"实际字段: {list(content.keys())}\n"
                f"完整响应: {content}"
            )
        id_value = content['id']
        jsonrpc_value = content['jsonrpc']
        id_valid = id_value == 1
        jsonrpc_valid = jsonrpc_value == '2.0'
        assert id_valid and jsonrpc_valid, (
            f"JSON-RPC 字段值断言失败\n"
            f"ID 断言: 期望=1, 实际={id_value} (类型: {type(id_value).__name__})\n"
            f"JSONRPC 断言: 期望='2.0', 实际='{jsonrpc_value}' (类型: {type(jsonrpc_value).__name__})\n"
            f"完整响应: {content}"
        )
    except AssertionError as e:
        logger.error(f"assert_id_and_version 断言失败: {e}")
        logger.error("用例失败！")
        raise e
    except Exception as e:
        logger.error(f"assert_id_and_version 执行异常: {type(e).__name__}: {e}")
        logger.error(f"断言参数类型: {type(content).__name__}, 内容: {content}")
        raise e


def assert_not_contains(content, expected='error'):
    try:
        content_str = str(content)
        contains_unexpected = expected in content_str
        assert not contains_unexpected, (
            f"不应包含内容断言失败\n"
            f"不应包含: '{expected}'\n"
            f"但在内容中发现了: {'是' if contains_unexpected else '否'}\n"
            f"内容摘要: {content_str[:200]}{'...' if len(content_str) > 200 else ''}\n"
            f"内容长度: {len(content_str)}\n"
            f"内容类型: {type(content).__name__}"
        )
    except AssertionError as e:
        logger.error(f"assert_not_contains 断言失败: {e}")
        logger.error("用例失败！")
        raise e
    except Exception as e:
        logger.error(f"assert_not_contains 执行异常: {type(e).__name__}: {e}")
        logger.error(f"断言参数 - expected: '{expected}', content 类型: {type(content).__name__}")
        raise e
