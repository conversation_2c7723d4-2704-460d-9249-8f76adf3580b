"""
统一配置管理器

功能：
- 统一的配置文件加载和管理
- 支持环境和自定义URL配置获取
- 配置文件缓存和热更新
- 支持pytest-xdist并发执行
- 文件锁和进程间共享缓存
- 线程安全的配置访问

特性：
- 合并原有的config_handler.py、config_manager.py、enhanced_config_manager.py功能
- 提供向后兼容的API接口
- 支持多种配置格式和环境
- 优化的性能和并发处理

使用方式：
    from common.config import get_config, get_config_by_env
    config = get_config_by_env('alphanet')
    data = get_config('config.yaml')
"""

import re
import os
import time
import threading
import fcntl
import tempfile
import pickle
from typing import Dict, Any, Optional
from urllib.parse import urlparse, urlunparse
import yaml
from loguru import logger
from common.handle_path import CONFIG_DIR, CASE_DIR


class UnifiedConfigManager:
    """
    统一配置管理器
    
    整合原有三个配置模块的功能：
    - config_handler.py: 环境配置获取和URL处理
    - enhanced_config_manager.py: 文件缓存和并发支持
    - config_manager.py: 统一接口管理
    """
    
    def get_config(self, file_name: str) -> Any:
        """
        获取配置数据（兼容旧接口）

        Args:
            file_name: 配置文件路径

        Returns:
            配置数据
        """
        return self.load_yaml(file_name)
    
    def __init__(self):
        """初始化统一配置管理器"""
        # 配置文件缓存机制（支持pytest-xdist并发）
        self._file_cache: Dict[str, Any] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._cache_lock = threading.RLock()
        self._file_locks: Dict[str, Any] = {}
        self._temp_dir = tempfile.gettempdir()
    
    def _get_file_lock(self, file_path: str):
        """获取文件锁，支持进程间同步"""
        if file_path not in self._file_locks:
            lock_file_name = f"config_lock_{abs(hash(file_path))}.lock"
            lock_file_path = os.path.join(self._temp_dir, lock_file_name)
            
            try:
                lock_file = open(lock_file_path, 'w')
                self._file_locks[file_path] = lock_file
            except Exception as e:
                logger.warning(f"创建文件锁失败 {file_path}: {e}")
                self._file_locks[file_path] = None
        
        return self._file_locks[file_path]

    def _acquire_file_lock(self, file_path: str, timeout: int = 5) -> bool:
        """获取文件锁"""
        lock_file = self._get_file_lock(file_path)
        if lock_file is None:
            return True
        
        try:
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    return True
                except BlockingIOError:
                    time.sleep(0.01)
            
            logger.warning(f"获取文件锁超时: {file_path}")
            return False
        except Exception as e:
            logger.warning(f"获取文件锁失败 {file_path}: {e}")
            return True

    def _release_file_lock(self, file_path: str):
        """释放文件锁"""
        lock_file = self._get_file_lock(file_path)
        if lock_file is None:
            return
        
        try:
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
        except Exception as e:
            logger.warning(f"释放文件锁失败 {file_path}: {e}")

    def _get_shared_cache_path(self, file_name: str) -> str:
        """获取共享缓存文件路径"""
        cache_name = f"config_cache_{abs(hash(file_name))}.pkl"
        return os.path.join(self._temp_dir, cache_name)

    def _load_from_shared_cache(self, file_name: str, expected_mtime: float) -> Optional[Any]:
        """从进程间共享缓存加载数据"""
        try:
            cache_path = self._get_shared_cache_path(file_name)
            if not os.path.exists(cache_path):
                return None
            
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
            
            if (cache_data.get('mtime') == expected_mtime and 
                cache_data.get('file_path') == file_name):
                return cache_data.get('data')
            
            return None
        except Exception as e:
            logger.trace(f"加载共享缓存失败 {file_name}: {e}")
            return None

    def _save_to_shared_cache(self, file_name: str, data: Any, mtime: float):
        """保存数据到进程间共享缓存"""
        try:
            cache_path = self._get_shared_cache_path(file_name)
            cache_data = {
                'file_path': file_name,
                'data': data,
                'mtime': mtime,
                'cached_at': time.time()
            }
            
            with open(cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
            
            logger.trace(f"已保存到共享缓存: {os.path.basename(file_name)}")
        except Exception as e:
            logger.trace(f"保存共享缓存失败 {file_name}: {e}")

    def load_yaml(self, file_path: str) -> Any:
        """
        读取yaml文件 - 支持pytest-xdist并发执行的缓存机制
        """
        with self._cache_lock:
            try:
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"配置文件不存在: {file_path}")
                
                current_mtime = os.path.getmtime(file_path)
                cached_mtime = self._file_timestamps.get(file_path, 0)
                
                if (file_path not in self._file_cache or 
                    current_mtime > cached_mtime):
                    
                    shared_data = self._load_from_shared_cache(file_path, current_mtime)
                    if shared_data is not None:
                        self._file_cache[file_path] = shared_data
                        self._file_timestamps[file_path] = current_mtime
                        logger.trace(f"从共享缓存加载: {os.path.basename(file_path)}")
                        return shared_data
                    
                    if self._acquire_file_lock(file_path):
                        try:
                            shared_data = self._load_from_shared_cache(file_path, current_mtime)
                            if shared_data is not None:
                                self._file_cache[file_path] = shared_data
                                self._file_timestamps[file_path] = current_mtime
                                logger.trace(f"其他进程已加载: {os.path.basename(file_path)}")
                                return shared_data
                            
                            with open(file_path, 'r', encoding='utf-8') as f:
                                yaml_data = yaml.safe_load(f)
                            
                            self._file_cache[file_path] = yaml_data
                            self._file_timestamps[file_path] = current_mtime
                            self._save_to_shared_cache(file_path, yaml_data, current_mtime)
                            
                            logger.debug(f"配置文件已重新加载: {os.path.basename(file_path)}")
                            
                        finally:
                            self._release_file_lock(file_path)
                    else:
                        if file_path in self._file_cache:
                            logger.warning(f"获取锁失败，使用现有缓存: {os.path.basename(file_path)}")
                        else:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                yaml_data = yaml.safe_load(f)
                            self._file_cache[file_path] = yaml_data
                            self._file_timestamps[file_path] = current_mtime
                            logger.warning(f"无锁直接读取: {os.path.basename(file_path)}")
                else:
                    logger.trace(f"使用缓存的配置文件: {os.path.basename(file_path)}")
                
                return self._file_cache[file_path]
                
            except Exception as e:
                logger.error(f'yaml文件读取失败，文件名称：{file_path}, 错误: {e}')
                return self._file_cache.get(file_path, {})

    def get_config_by_env(self, env_name: str) -> Dict[str, Any]:
        """
        根据环境名称获取配置
        
        Args:
            env_name: 环境名称 (alphanet, stage, testnet)
            
        Returns:
            环境配置字典
        """
        config_data = self.load_yaml(CONFIG_DIR)
        if not config_data:
            return {}

        env_config = config_data.get('url', {}).get(env_name)
        if env_config is not None:
            logger.info(f'成功加载预定义测试环境：{env_name}')
            return env_config
        else:
            logger.warning(f'测试环境参数 "{env_name}" 不是有效的预定义环境名称')
            return {}

    def get_config_by_custom_url(self, custom_url_input: str) -> Dict[str, Any]:
        """
        根据自定义 URL 或 IP 获取并替换配置
        
        Args:
            custom_url_input: 自定义URL或IP
            
        Returns:
            替换后的配置字典
        """
        config_data = self.load_yaml(CONFIG_DIR)
        if not config_data:
            return {}

        base_config = config_data.get('url', {}).get('alphanet', {})
        if not isinstance(base_config, dict):
            logger.warning(f'配置文件 {CONFIG_DIR} 中 \'url.alphanet\' 结构无效或不存在，无法进行域名替换')
            return {}

        processed_url = self._process_custom_url(custom_url_input)
        if not processed_url:
            return {}

        try:
            target_parsed = urlparse(processed_url)
            target_hostname = target_parsed.hostname
            if not target_hostname:
                raise ValueError("无法从提供的输入解析主机名")
        except ValueError as e:
            logger.error(f'处理输入 "{custom_url_input}" 时出错: {e}')
            return {}

        logger.info(f'将使用自定义主机名 {target_hostname} 替换 alphanet 环境中的 URL')
        replaced_config = self._replace_hostname_in_urls(base_config, target_hostname)
        replaced_config['default'] = processed_url
        return replaced_config

    def _process_custom_url(self, url_input: str) -> Optional[str]:
        """处理自定义 URL 或 IP，添加必要的协议前缀"""
        if url_input.startswith(('http://', 'https://')):
            return url_input
        if re.fullmatch(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', url_input):
            processed = f'https://{url_input}'
            logger.info(f'检测到IP地址输入，处理后URL为: {processed}')
            return processed
        if '.' in url_input:
            return f'https://{url_input}'
        return None

    def _replace_hostname_in_urls(self, config_level: Dict[str, Any], target_hostname: str) -> Dict[str, Any]:
        """递归替换 URL 中的主机名"""
        new_level = {}
        for key, value in config_level.items():
            if isinstance(value, str) and value.startswith(('http://', 'https://', 'ws://', 'wss://')):
                try:
                    original_parsed = urlparse(value)
                    new_netloc = target_hostname
                    if original_parsed.port:
                        new_netloc += f":{original_parsed.port}"

                    new_url = urlunparse((
                        original_parsed.scheme, new_netloc, original_parsed.path,
                        original_parsed.params, original_parsed.query, original_parsed.fragment
                    ))
                    new_level[key] = new_url
                except Exception as e:
                    logger.warning(f'解析或重建URL "{value}" 时出错: {e}. 保留原始值。')
                    new_level[key] = value
            elif isinstance(value, dict):
                new_level[key] = self._replace_hostname_in_urls(value, target_hostname)
            else:
                new_level[key] = value
        return new_level

    def is_custom_url(self, env_input: str) -> bool:
        """
        判断输入是否为 URL 或 IP
        
        Args:
            env_input: 输入字符串
            
        Returns:
            是否为自定义URL或IP
        """
        return (
            '.' in env_input or
            re.fullmatch(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', env_input) or
            env_input.startswith(('http://', 'https://'))
        )

    def get_case_data(self) -> Dict[str, Any]:
        """获取测试用例数据"""
        return self.load_yaml(CASE_DIR)

    def get_headers(self) -> Dict[str, Any]:
        """获取请求头配置"""
        config = self.load_yaml(CONFIG_DIR)
        return config.get('request_headers', {}).get('headers', {})

    def get_url_config(self, env_name: str = 'alphanet') -> Dict[str, Any]:
        """获取指定环境的URL配置"""
        config = self.load_yaml(CONFIG_DIR)
        return config.get('environments', {}).get(env_name, {})

    def reload_config(self, file_name: Optional[str] = None):
        """重新加载配置文件"""
        with self._cache_lock:
            if file_name:
                if file_name in self._file_cache:
                    del self._file_cache[file_name]
                    del self._file_timestamps[file_name]
                self._clear_shared_cache(file_name)
                self.load_yaml(file_name)
                logger.info(f"已重新加载配置文件: {os.path.basename(file_name)}")
            else:
                cached_files = list(self._file_cache.keys())
                self._file_cache.clear()
                self._file_timestamps.clear()
                for cached_file in cached_files:
                    self._clear_shared_cache(cached_file)
                for cached_file in cached_files:
                    self.load_yaml(cached_file)
                logger.info("已重新加载所有缓存的配置文件")

    def clear_cache(self):
        """清空配置缓存"""
        with self._cache_lock:
            cached_files = list(self._file_cache.keys())
            for cached_file in cached_files:
                self._clear_shared_cache(cached_file)
            self._file_cache.clear()
            self._file_timestamps.clear()
            logger.info("配置缓存已清空")

    def _clear_shared_cache(self, file_name: str):
        """清理指定文件的共享缓存"""
        try:
            cache_path = self._get_shared_cache_path(file_name)
            if os.path.exists(cache_path):
                os.remove(cache_path)
                logger.trace(f"已清理共享缓存: {os.path.basename(file_name)}")
        except Exception as e:
            logger.trace(f"清理共享缓存失败 {file_name}: {e}")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._cache_lock:
            shared_caches = []
            for file_path in self._file_cache.keys():
                cache_path = self._get_shared_cache_path(file_path)
                if os.path.exists(cache_path):
                    try:
                        cache_stat = os.stat(cache_path)
                        shared_caches.append({
                            'file': os.path.basename(file_path),
                            'cache_size': cache_stat.st_size,
                            'cache_mtime': cache_stat.st_mtime
                        })
                    except Exception:
                        pass
            
            return {
                'local_cached_files': [os.path.basename(f) for f in self._file_cache.keys()],
                'local_cache_size': len(self._file_cache),
                'file_timestamps': {os.path.basename(k): v for k, v in self._file_timestamps.items()},
                'shared_caches': shared_caches,
                'process_id': os.getpid(),
                'temp_dir': self._temp_dir
            }


# 创建默认实例
default_config_manager = UnifiedConfigManager()

# 向后兼容的便捷函数
def load_yaml(file_path: str) -> Any:
    """加载YAML文件的便捷函数"""
    return default_config_manager.load_yaml(file_path)

def get_config_by_env(env_name: str) -> Dict[str, Any]:
    """根据环境名称获取配置的便捷函数"""
    return default_config_manager.get_config_by_env(env_name)

def get_config_by_custom_url(custom_url_input: str) -> Dict[str, Any]:
    """根据自定义URL获取配置的便捷函数"""
    return default_config_manager.get_config_by_custom_url(custom_url_input)

def is_custom_url(env_input: str) -> bool:
    """判断输入是否为自定义URL的便捷函数"""
    return default_config_manager.is_custom_url(env_input)

def get_case_data() -> Dict[str, Any]:
    """获取测试用例数据的便捷函数"""
    return default_config_manager.get_case_data()

def get_headers() -> Dict[str, Any]:
    """获取请求头配置的便捷函数"""
    return default_config_manager.get_headers()

def get_url_config(env_name: str = 'alphanet') -> Dict[str, Any]:
    """获取URL配置的便捷函数"""
    return default_config_manager.get_url_config(env_name)

def reload_config(file_name: Optional[str] = None):
    """重新加载配置的便捷函数"""
    return default_config_manager.reload_config(file_name)

def clear_cache():
    """清空缓存的便捷函数"""
    return default_config_manager.clear_cache()

def get_cache_info() -> Dict[str, Any]:
    """获取缓存信息的便捷函数"""
    return default_config_manager.get_cache_info()

# 提供统一的管理器实例
def get_config_manager():
    """获取配置管理器实例"""
    return default_config_manager

def get_config(file_name: str) -> Any:
    """获取配置数据的便捷函数"""
    return default_config_manager.load_yaml(file_name)

# 添加包装器需要的方法
def get_config_data(file_name: str) -> Any:
    """获取配置数据（用于包装器）"""
    return default_config_manager.load_yaml(file_name)