from typing import Optional
from loguru import logger
import httpx
import json
from websocket import create_connection
from common.ip_mode_handler import IPModeHandler
from common.config import get_config_manager
from common.handle_path import CONFIG_DIR
from urllib.parse import urlparse


class HttpClient:
    _ip_mode_handler: Optional[IPModeHandler] = None
    _config_manager = get_config_manager()

    @classmethod
    def _build_url_paths_mapping(cls, config_data=None):
        url_paths = {}
        if config_data is None:
            config_data = cls._config_manager.get_config(CONFIG_DIR) or {}
        if 'url' in config_data and 'alphanet' in config_data['url']:
            alphanet_config = config_data['url']['alphanet']

            def extract_paths(config_level, base_name=None):
                for key, value in config_level.items():
                    current_name = f"{base_name}_{key}" if base_name else key
                    if isinstance(value, str) and value.startswith(('http://', 'https://')):
                        try:
                            parsed_url = urlparse(value)
                            path = parsed_url.path.rstrip('/')
                            if path:
                                url_paths[path] = current_name
                        except Exception as e:
                            logger.warning(f"解析配置文件 URL '{value}' 失败: {e}")
                    elif isinstance(value, dict):
                        extract_paths(value, current_name)

            extract_paths(alphanet_config)
        return url_paths

    @classmethod
    def _ensure_ip_handler(cls):
        if cls._ip_mode_handler is None:
            config_data = cls._config_manager.get_config(CONFIG_DIR) or {}
            cls._ip_mode_handler = IPModeHandler(cls._build_url_paths_mapping(config_data))

    @classmethod
    def send_http(cls, data: dict):
        cls._ensure_ip_handler()
        request_url = data.get('url', '')
        request_headers = data.get('headers', {}).copy() if data.get('headers') else {}
        verify_ssl = True
        is_ip_mode, original_env_value = cls._ip_mode_handler.is_ip_mode()
        if is_ip_mode:
            verify_ssl, _ = cls._ip_mode_handler.apply_ip_mode_config(request_headers, request_url, original_env_value)

        data_to_send = data.copy()
        data_to_send['headers'] = request_headers
        try:
            response = httpx.request(**data_to_send, timeout=60.0, verify=verify_ssl)
            logger.info(f"响应状态码: {response.status_code}")
            return response
        except httpx.RequestError as e:
            logger.error(f"发送请求失败，原始请求参数为：{data}")
            logger.debug(f"失败请求详情（含处理后headers和verify={verify_ssl}）: {data_to_send}")
            logger.error(f"发生的错误为：{e}")
            raise e

    @staticmethod
    def websocket_connection(url: str):
        try:
            ws = create_connection(url, timeout=20)
            logger.info(f"WebSocket连接成功，响应状态码为：{ws.getstatus()}")
        except Exception as e:
            logger.error('创建WebSocket连接失败')
            logger.exception(f'发生的错误为：{e}')
            raise e
        return ws

    @staticmethod
    def send_websocket(ws, data: dict):
        try:
            ws.send(json.dumps(data))
            logger.info(f'发送数据成功：{data}')
            response = ws.recv()
        except Exception as e:
            logger.error(f'发送数据失败，请求参数为：{data}')
            logger.exception(f'发生的错误为：{e}')
            raise e
        else:
            return response
