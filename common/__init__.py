"""
Common模块包

这个包包含了项目的核心工具和通用功能模块。

核心模块：
- handle_path: 路径管理和配置
- config: 统一配置管理器
- utils: 核心工具类
- wrapper: 装饰器模块
- ip_mode_handler: IP模式处理
- types: 类型定义

使用方式：
    from common.utils import Utils
    from common.config import get_config_by_env
    from common.types import JsonDict, ConfigDict
"""

__all__ = [
    'handle_path',
    'config',
    'utils',
    'wrapper',
    'ip_mode_handler',
    'types'
]