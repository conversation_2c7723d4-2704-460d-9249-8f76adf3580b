from pathlib import Path
from typing import Optional
import yaml
from loguru import logger


def read_yaml(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        logger.error(f"YAML 文件未找到: {file_path}")
        return None
    except yaml.YAMLError as e:
        logger.error(f"解析 YAML 文件时出错: {file_path}, Error: {e}")
        return None
    except Exception as e:
        logger.error(f"读取 YAML 文件时发生未知错误: {file_path}, Error: {e}")
        return None


def handle_template(source_data, replace_data: dict):
    from string import Template
    import yaml

    res = Template(str(source_data)).safe_substitute(**replace_data)
    return yaml.safe_load(res)


def handle_yaml(file_name: Path) -> Optional[dict]:
    from common.config import get_config_manager

    try:
        yaml_data = get_config_manager().get_config(file_name)
        if yaml_data is None:
            raise FileNotFoundError(f"无法加载配置文件: {file_name}")
        return yaml_data
    except Exception as e:
        logger.error(f"YAML文件读取失败，文件名称：{file_name}, 错误: {e}")
        raise e
