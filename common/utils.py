from typing import Optional
from loguru import logger
import base64
import hashlib

from common.http_client import HttpClient
from common.assertion_utils import (
    assert_status_and_nodeid,
    assert_contains,
    assert_id_and_version,
    assert_not_contains,
)
from common.file_handler import handle_yaml, handle_template, read_yaml


class Utils:
    @classmethod
    def send_http(cls, data):
        return HttpClient.send_http(data)

    @classmethod
    def websocket_connection(cls, url: str):
        return HttpClient.websocket_connection(url)

    @classmethod
    def send_websocket(cls, ws, data):
        return HttpClient.send_websocket(ws, data)

    @classmethod
    def handle_yaml(cls, file_name):
        return handle_yaml(file_name)

    @classmethod
    def handle_template(cls, source_data, replace_data: dict):
        return handle_template(source_data, replace_data)

    @staticmethod
    def assert_status_and_nodeid(response):
        return assert_status_and_nodeid(response)

    @staticmethod
    def assert_contains(content, expected='0x'):
        return assert_contains(content, expected)

    @staticmethod
    def assert_id_and_version(content):
        return assert_id_and_version(content)

    @staticmethod
    def assert_not_contains(content, expected='error'):
        return assert_not_contains(content, expected)

    @staticmethod
    def tx_decoder(encoded_tx):
        decoded_tx = base64.b64decode(encoded_tx)
        tx_hash = hashlib.sha256(decoded_tx).hexdigest()
        return tx_hash
