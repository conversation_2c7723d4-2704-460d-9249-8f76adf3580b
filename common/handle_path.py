"""
路径处理模块

功能：
- 定义项目中所有目录和文件的路径常量
- 提供统一的路径管理，避免硬编码路径
- 支持跨平台路径处理

主要路径：
- BASE_DIR: 项目根目录
- CONF_DIR: 配置文件目录
- CONFIG_DIR: 主配置文件路径
- DATA_DIR: 测试数据目录
- CASE_DIR: 测试用例数据文件路径
- LOG_DIR: 日志文件目录

使用方式：
    from common.handle_path import CONFIG_DIR, CASE_DIR
    config = load_yaml(CONFIG_DIR)
"""

import os

# 获取当前文件的上一级目录，确定根目录
BASE_DIR = os.path.dirname(os.path.dirname(__file__))
# 构建配置文件的路径
CONF_DIR = os.path.join(BASE_DIR, "conf")  # 将多个路径组合成一个路径
CONFIG_DIR = os.path.join(CONF_DIR, 'config.yaml')
# 构建用例数据目录的路径
DATA_DIR = os.path.join(BASE_DIR, "data")
# 指向统一的测试数据文件
CASE_DIR = os.path.join(DATA_DIR, "case_data.yaml")
# 构建日志文件目录的路径
LOG_DIR = os.path.join(BASE_DIR, "log")

