from common.handle_path import CONFIG_DIR
from common.utils import Utils
from common.wrapper import api_call
from common.config import get_config_manager
from common.data_selector import get_data_file_path


class CosmosApi:

    # 为了向后兼容，保留类级别的data属性
    _config_manager = get_config_manager()
    # 对于Cosmos API，使用非EVM链数据文件
    data = _config_manager.load_yaml(get_data_file_path('non_evm'))
    headers = _config_manager.get_headers()

    @api_call
    def accounts(self, url, path):
        """返回所有现有帐户"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def account_details(self, url, path):
        """根据地址返回帐户详细信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def params(self, url, path):
        """查询所有参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def grants(self, url, path):
        """返回授予者授予受让人的授权列表"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def grantee(self, url, path):
        """返回受让人的 GrantAuthorization 列表"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def granter(self, url, path):
        """返回授予者授予的 GrantAuthorization 列表"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def balances(self, url, path):
        """查询单个账户所有币的余额"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def balance(self, url, path):
        """查询单个账户单个代币的余额"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def denoms_metadata(self, url, path):
        """查询所有已注册token面额的客户端元数据"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def denom_metadata(self, url, path):
        """查询给定硬币面额的客户端元数据"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def bank_params(self, url, path):
        """查询bank模块的参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def bank_params(self, url, path):
        """查询bank模块的参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def spendable_balances(self, url, path):
        """查询单个账户所有币的可消费余额"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def total_supply(self, url, path):
        """查询所有币的总供应量"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def supply(self, url, path):
        """查询单个币的供应量"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_by_height(self, url, path):
        """查询给定高度的块"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def latest_block(self, url, path):
        """返回最新的块"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def node_info(self, url, path):
        """查询当前节点信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def node_syncing(self, url, path):
        """返回节点同步的状态"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validatorsets(self, url, path):
        """查询给定高度的验证器集"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def latest_validatorsets(self, url, path):
        """查询最新的验证器集"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def community_pool(self, url, path):
        """查询社区池币"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def delegator_rewards(self, url, path):
        """查询每个委托人获得的总奖励"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def delegation_rewards(self, url, path):
        """查询委托获得的总奖励"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators_of_delegator(self, url, path):
        """查询委托人的验证人"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def delegator_withdraw_address(self, url, path):
        """查询委托人的提现地址"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def distribution_params(self, url, path):
        """查询分发模块参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators_commisson(self, url, path):
        """查询验证人的累计佣金"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators_rewards(self, url, path):
        """查询验证人地址的奖励"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validator_slashes(self, url, path):
        """查询验证器的斜杠事件"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def evidence(self, url, path):
        """查询所有证据"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def granted_fee(self, url, path):
        """检索授予者授予受让人的费用"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def address_grants(self, url, path):
        """检索某个地址的所有授权"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def grants_give_address(self, url, path):
        """返回某个地址给予的所有赠款"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def gov_params(self, url, path):
        """查询gov模块的所有参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def proposals(self, url, path):
        """根据给定状态查询所有的提案"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def proposal_id(self, url, path):
        """根据提案id查询提案"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def proposal_deposits(self, url, path):
        """查询单个提案的所有存款"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def proposal_vote(self, url, path):
        """查询提案投票的计数结果"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def votes_of_proposal(self, url, path):
        """查询给定提案的投票"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def slashing_params(self, url, path):
        """查询惩罚模块的参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators_signing_info(self, url, path):
        """查询所有验证者的签名信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def delegator_delegations(self, url, path):
        """查询给定委托人地址的所有委托"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def address_redeleations(self, url, path):
        """查询给定地址的重新委派"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def address_unbonding_redeleations(self, url, path):
        """查询给定委托人地址的所有未绑定委托"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def address_validators_info(self, url, path):
        """查询给定委托人地址的所有验证人信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def staking_params(self, url, path):
        """查询质押参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def pool_info(self, url, path):
        """查询池信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators_status(self, url, path):
        """查询所有与给定状态匹配的验证器"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators_address(self, url, path):
        """查询给定验证器地址的验证器信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def delegate_info(self, url, path):
        """查询给定验证器的委托信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def unbonding_delegation(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validator_unbonding_delegations(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_decoded_txs(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def tx_by_hash(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def applied_plan(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def current_plan(self, url, path):
        """查询当前的升级计划"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def module_versions(self, url, path):
        """从状态中查询模块版本列表"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def abci_info_rest(self, url, path):
        """获取有关应用程序的信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def abci_info_jsonrpc(self, url, **data):
        """获取有关应用程序的信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_rest(self, url, path):
        """检索指定高度处的块"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_jsonrpc(self, url, **data):
        """检索指定高度处的块"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_by_hash_rest(self, url, path):
        """通过哈希检索区块"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_by_hash_jsonrpc(self, url, **data):
        """通过哈希检索区块"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_results_rest(self, url, path):
        """检索指定高度的块结果"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_results_jsonrpc(self, url, **data):
        """检索指定高度的块结果"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def blockchain_rest(self, url, path):
        """获取 minHeight <= height <= maxHeight 的块头"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def blockchain_jsonrpc(self, url, **data):
        """获取 minHeight <= height <= maxHeight 的块头"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def commit_rest(self, url, path):
        """检索指定高度的提交结果"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def commit_jsonrpc(self, url, **data):
        """检索指定高度的提交结果"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def consensus_params_rest(self, url, path):
        """获取共识参数"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def consensus_params_jsonrpc(self, url, **data):
        """获取共识参数"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def consensus_state_rest(self, url, path):
        """获取共识状态"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def consensus_state_jsonrpc(self, url, **data):
        """获取共识状态"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def dump_consensus_state_rest(self, url, path):
        """获取共识状态"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def dump_consensus_state_jsonrpc(self, url, **data):
        """获取共识状态"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def genesis_chunked_rest(self, url, path):
        """以多个块的形式检索创世记"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def genesis_chunked_jsonrpc(self, url, **data):
        """以多个块的形式检索创世记"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def health_rest(self, url, path):
        """获取节点健康状况"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def health_jsonrpc(self, url, **data):
        """获取节点健康状况"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def num_unconfirmed_txs_rest(self, url, path):
        """获取有关未确认交易的数据"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def num_unconfirmed_txs_jsonrpc(self, url, **data):
        """获取有关未确认交易的数据"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def status_rest(self, url, path):
        """获取 Tendermint 状态"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def status_jsonrpc(self, url, **data):
        """获取 Tendermint 状态"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def tx_rest(self, url, path):
        """获取交易"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def net_info_rest(self, url, path):
        """获取网络信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def net_info_jsonrpc(self, url, **data):
        """获取网络信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators_rest(self, url, path):
        """获取验证器"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def check_tx_rest(self, url, path):
        """检查交易而不执行它"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def check_tx_jsonrpc(self, url, **data):
        """检查交易而不执行它"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response