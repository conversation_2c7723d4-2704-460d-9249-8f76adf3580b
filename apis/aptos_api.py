from common.handle_path import CASE_DIR
from common.utils import Utils
from common.wrapper import api_call
from common.config import get_config_manager


class AptosApi:

    # 为了向后兼容，保留类级别的data属性
    _config_manager = get_config_manager()
    data = _config_manager.get_case_data()

    @api_call
    def get_account(self, url, path):
        """返回账户地址的认证密钥和序列号"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_account_resources(self, url, path):
        """检索给定账户和特定账本版本的所有账户资源"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_account_modules(self, url, path):
        """检索给定账户在特定账本版本中的所有账户模块的字节码"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_account_resource(self, url, path):
        """从给定账户和特定账本版本检索单个资源"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_account_module(self, url, path):
        """从给定账户和特定账本版本检索单个模块"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_blocks_by_height(self, url, path):
        """按照区块高度获取区块交易和相应的区块信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_blocks_by_version(self, url, path):
        """获取块中的交易以及给定块中版本的相应块信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_events_by_creation_number(self, url, path):
        """根据给定地址的创建编号检索事件"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_events_by_event_handel(self, url, path):
        """检索发送到与该事件类型匹配的给定帐户的事件"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def show_openapi_explorer(self, url, path):
        """提供一个 UI 来探索 API 并直接从 /spec.yaml 和 /spec.json 检索信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def check_basic_node_health(self, url, path):
        """检查基本节点健康"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_ledger_info(self, url, path=''):
        """获取最新的账本信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_transactions(self, url, path):
        """获取交易记录"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_transaction_by_hash(self, url, path):
        """通过哈希获取交易"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def get_account_transactions(self, url, path):
        """获取账户交易记录"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def estimate_gasprice(self, url, path):
        """估计汽油价格"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response