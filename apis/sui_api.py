from common.handle_path import CASE_DIR, CONFIG_DIR
from common.utils import Utils
from common.wrapper import api_call
from common.config import get_config_manager


class SuiApi:

    # 为了向后兼容，保留类级别的data属性
    _config_manager = get_config_manager()
    data = _config_manager.get_case_data()
    headers = _config_manager.get_headers()

    @api_call
    def suix_get_all_balances(self, url, **data):
        """返回所有币种的总币余额"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_all_coins(self, url, **data):
        """返回一个地址拥有的所有 Coin 对象"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_balance(self, url, **data):
        """返回某一币种由地址所有者拥有的总币余额"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_coin_metadata(self, url, **data):
        """返回代币的元数据"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_coins(self, url, **data):
        """返回一个地址拥有的所有 Coin 对象"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_total_supply(self, url, **data):
        """返回硬币的总供应量"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_latest_sui_system_state(self, url, **data):
        """返回链上的最新 SUI 系统状态对象"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_reference_gas_price(self, url, **data):
        """返回网络的参考 Gas 价格"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def suix_get_validators_apy(self, url, **data):
        """ 返回验证器 APY"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_move_function_arg_types(self, url, **data):
        """根据规范化类型，返回 Move 函数的参数类型"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_move_function_arg_types(self, url, **data):
        """根据规范化类型，返回 Move 函数的参数类型"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_normalized_move_function(self, url, **data):
        """返回 Move 函数的结构化表示"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_normalized_move_module(self, url, **data):
        """返回 Move 模块的结构化表示"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_normalized_move_modules_by_package(self, url, **data):
        """返回给定包中所有模块的结构化表示"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_normalized_move_struct(self, url, **data):
        """返回 Move 结构的结构化表示"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_chain_identifier(self, url, **data):
        """返回链条创世检查点哈希的前四个字节"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_checkpoint(self, url, **data):
        """ 返回检查点"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_checkpoints(self, url, **data):
        """返回分页的检查点列表"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_events(self, url, **data):
        """返回交易事件"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_latest_checkpoint_sequence_number(self, url, **data):
        """返回已执行的最新检查点的序列号"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_protocol_config(self, url, **data):
        """返回给定版本号的协议配置表"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_protocol_config(self, url, **data):
        """返回给定版本号的协议配置表"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_total_transaction_blocks(self, url, **data):
        """返回服务器所知的交易块总数"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_get_transaction_block(self, url, **data):
        """返回交易响应对象"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_multi_get_objects(self, url, **data):
        """返回对象列表的对象数据"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_try_get_past_object(self, url, **data):
        """返回对象列表的对象数据"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def sui_try_multi_get_past_objects(self, url, **data):
        """返回指定版本的对象信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response


    @api_call
    def sui_multi_get_transaction_blocks(self, url, **data):
        """返回一个交易响应的有序列表"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response