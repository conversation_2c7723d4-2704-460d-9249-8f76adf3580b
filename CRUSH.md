# CRUSH.md

本仓库速查手册（供自动化智能体使用）。保持简洁，遵循现有配置与约定。

## 构建/安装/运行
- 安装依赖: pip install -r requirements.txt
- 运行全部测试: pytest
- 单文件: pytest testcases/test_ethereum.py -v -s
- 单用例: pytest testcases/test_ethereum.py::TestEthereum::test_method_name -v -s
- 按标记: pytest -m bsc  或  pytest -m "evm and not bsc"
- 指定环境: pytest --env alphanet  或  pytest --env https://custom-node:8545
- 关键字过滤: pytest -k ethereum
- 并行: pytest -n 4  或  pytest -n auto

## 质量检查
- 代码格式: black .
- 导入排序: isort .
- 代码静态检查: flake8 .
- 类型检查: mypy .

## 代码风格指南
- 语言版本: Python >=3.8；行宽 100（Black/Flake8 已配置）
- 导入: isort profile=black，顺序 FUTURE/stdlib/thirdparty/firstparty/local；known_first_party=[common, apis, testcases]
- 格式化: 使用 black；保持包含与排除目录与 pyproject.toml 一致
- 类型: 启用严格 mypy；新代码必须类型标注（函数/装饰器/返回值）；避免 Any；no_implicit_optional
- 命名: 文件/模块下划线风格；类名驼峰；函数/变量小写下划线；常量全大写
- 错误处理: 捕获最小化异常范围；记录 loguru 日志；不吞异常；对网络/IO设置合理超时与重试（见 common/utils.py 调用模式）
- 日志: 使用 loguru；测试开始/结束已在 conftest.py 中统一打点
- 测试: 所有测试放在 testcases/；使用 pytest 标记体系（见 pyproject.toml markers）；遵循 Test* 类与 test_* 函数命名
- 配置: 通过 conf/config.yaml 与 --env 参数；支持自定义 URL/IP（common/ip_mode_handler.py）
- 依赖: 固定版本见 requirements.txt；开发工具在 pyproject optional-dependencies.dev

## 其他约定
- 不提交密钥/环境文件（.env* 已忽略）；避免将日志/构建产物加入版本库
- 无 Cursor/Copilot 专属规则文件；若新增 .cursor/rules/ 或 .github/copilot-instructions.md，请在此补充
- 参考: README.md 获取快速上手；pyproject.toml 获取完整 pytest/mypy/black/isort/flake8 配置
